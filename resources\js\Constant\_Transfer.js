const _Transfer = {
    TRANSFER_TYPES: {
        inbound: "inbound",
        outbound: "outbound",
    },

    TRANSACTIONS: {
        inbound: {
            conflict: {value: "conflict", name: "Invalid Requests", status: ['clientConflict']},
            pending: {value: "pending", name: "Pending", status: ['pendingRequest', 'pendingApproval']},
            previous: {
                value: "previous",
                name: "Inbound Transfer History",
                status: [
                    'inbound.clientCancelled',
                    'inbound.clientApproved',
                    'inbound.serverApproved',
                    'inbound.clientRejected',
                    'inbound.serverRejected',
                    'systemCancelled'
                ],
                forFilterStatus: [
                    'clientCancelled',
                    'clientApproved',
                    'serverApproved',
                    'clientRejected',
                    'serverRejected',
                    'systemCancelled'
                ]
            }
        },
        outbound: {
            required: {value: "required", name: "Pending", status: ['pending']},
            previous: {
                value: "previous",
                name: "Outbound Transfer History",
                status: [
                    'outbound.clientCancelled',
                    'outbound.clientApproved',
                    'outbound.serverApproved',
                    'outbound.clientRejected',
                    'outbound.serverRejected'
                ],
                forFilterStatus: [
                    'clientCancelled',
                    'clientApproved',
                    'serverApproved',
                    'clientRejected',
                    'serverRejected'
                ]
            }
        }
    },

    ACTION_TYPES: {
        edit: 'update',
        cancel: 'cancel',
        retry: 'retry',
        approve: 'approve',
        reject: 'reject'
    },

    STATUS_TYPES: {
        conflict: 'clientConflict',
        pending: 'pending',
        pendingRequest: 'pendingRequest',
        pendingApproval: 'pendingApproval',
        inboundCancelled: 'inbound.clientCancelled',
        inboundApproved: 'inbound.clientApproved',
        inboundRejected: 'inbound.clientRejected',
        inboundServerApproved: 'inbound.serverApproved',
        outboundCancelled: 'outbound.clientCancelled',
        outboundApproved: 'outbound.clientApproved',
        outboundRejected: 'outbound.clientRejected',
        outboundServerApproved: 'outbound.serverApproved',
        systemCancelled: 'systemCancelled',
    },

    DUMMY_DATA: [
        { id: 1, domain_name: "bluedragonenterprises.net", updated_at: "2023-11-16 14:46:19", created_at: "2023-11-17 04:45:19", status: "clientConflict", deleted_at: null, error_code: 2303, error_message: 'Domain name does not exist.' },
        { id: 2, domain_name: "deepspacediplomacy.com", updated_at: "2023-11-15 14:46:19", created_at: "2023-11-16 04:45:19", status: "clientConflict", deleted_at: null, error_code: 2201, error_message: 'Invalid authorization code.' },
        { id: 3, domain_name: "quantumharbor.org", updated_at: "2023-11-14 14:46:19", created_at: "2023-11-15 04:45:19", status: "clientConflict", deleted_at: null, error_code: 2304, error_message: 'Locked Domain.' }, 
        { id: 4, domain_name: "test.org", updated_at: "2023-11-14 14:46:19", created_at: "2023-11-15 04:45:19", status: "clientConflict", deleted_at: null, error_code: 2202, error_message: 'For Edit Test.' }
    ],

    MAX_DOMAIN_TRANSFER: 500
};

_Transfer.ERRORS = {
    2003: { code: "2003", message: 'Missing authentication code.', actions: [_Transfer.ACTION_TYPES.cancel] },
    2005: { code: "2005", message: 'The domain format is invalid.', actions: [_Transfer.ACTION_TYPES.cancel] },
    2104: { code: "2104", message: 'An unexpected error occurred. Please contact support for assistance.', actions: [_Transfer.ACTION_TYPES.retry, _Transfer.ACTION_TYPES.cancel]},
    2106: { code: "2106", message: 'Domain is not eligible for transfer.', actions: [_Transfer.ACTION_TYPES.retry, _Transfer.ACTION_TYPES.cancel]},
    2201: { code: "2201", message: 'Authorization error.', actions: [_Transfer.ACTION_TYPES.cancel] },
    2202: { code: "2202", message: 'Invalid authentication code.', actions: [_Transfer.ACTION_TYPES.cancel] },
    2300: { code: "2300", message: 'The domain is already in a pending state.', actions: [_Transfer.ACTION_TYPES.cancel] },
    2303: { code: "2303", message: 'The domain does not exist.', actions: [_Transfer.ACTION_TYPES.cancel] },
    2304: { code: "2304", message: 'The domain is currently locked.', actions: [_Transfer.ACTION_TYPES.retry, _Transfer.ACTION_TYPES.cancel] }
};

export default _Transfer;