<?php

namespace App\Modules\Transfer\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\RegisteredDomain\Services\DomainRegistrationService;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use App\Modules\Transfer\Constants\TransferTransactionTypes;
use App\Modules\Transfer\Jobs\UpdateTransferFromPoll;
use App\Traits\UserContact;
use App\Util\Helper\Domain\DomainParser;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use stdClass;

class EppTransferService
{
    use UserContact, UserLoggerTrait;

    public static function instance()
    {
        $eppDomainService = new self;

        return $eppDomainService;
    }

    public function updateTransferStatusFromPoll($data)
    {
        if (empty($data)) {
            return;
        }

        foreach ($data as $item) {
            $pollId = DB::table('polls')->where('server_id', $item['id'])->value('id');
            $status = $item['status'];
            $name = strtolower($item['name']);

            UpdateTransferFromPoll::dispatch($pollId, $status, $name);
        }
    }

    public function domainTransferUpdate(stdClass $localInfo, string $transferredIn): void
    {
        $payloads = $this->extractDomainUpdatePayload($localInfo, $transferredIn);
        $this->updateDomainDetails($payloads);
    }

    public function callEppTransferRequest(string $domain, string $authCode, string $email): array
    {
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return ['status' => Config::get('transfer.response.error')];
        }

        $path = Config::get('transfer.'.TransferTransactionTypes::REQUEST);
        $authDecrypted = $this->decryptAuthCode($authCode, $email);
        $payload = ['name' => $domain, 'authentication' => $authDecrypted];

        return $this->sendHttpPostRequest($registry, $path, $payload, $email);
    }

    public function callEppTransferClientResponse(string $domain, string $action, string $email): array
    {
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return ['status' => Config::get('transfer.response.error')];
        }

        $path = Config::get('transfer.'.$action);
        $payload = ['name' => $domain];

        return $this->sendHttpPostRequest($registry, $path, $payload, $email);
    }

    public function callDatastoreTransferApproved(string $domain, string $status): array
    {
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return ['status' => Config::get('transfer.response.error')];
        }

        $path = Config::get('transfer')[$status];
        $payload = ['name' => $domain];

        return $this->sendHttpPostRequest($registry, $path, $payload, 'Cron:');
    }

    public function callTransferQuery($domain, $email): array
    {
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return ['status' => Config::get('transfer.response.error')];
        }

        $path = Config::get('transfer.'.TransferTransactionTypes::QUERY);
        $payload = ['name' => $domain];

        return $this->sendHttpPostRequest($registry, $path, $payload, $email);
    }

    public function callEppTransferServerReject(string $domain, string $email): array
    {
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return ['status' => Config::get('transfer.response.error')];
        }

        $path = Config::get('transfer.'.TransferTransactionTypes::REJECT);
        $payload = ['name' => $domain];

        return $this->sendHttpPostRequest($registry, $path, $payload, $email);
    }

    // PRIVATE Functions

    private function decryptAuthCode(string $authCode, string $email): ?string
    {
        try {
            return Crypt::decryptString($authCode);
        } catch (DecryptException $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));

            return null;
        }
    }

    private function sendHttpPostRequest(string $registry, string $path, array $payload, string $email): array
    {
        try {
            $request = Http::transfer($registry)->post($path, $payload);
        } catch (RequestException $e) {
            return $e->response->json();
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected Response');
        }

        return $request->json();
    }

    private function extractDomainUpdatePayload($localInfo, string $transferredIn): array
    {
        $name = strtolower($localInfo->domain_name);
        $eppDomainInfo = EppDomainService::instance()->callEppDomainInfo($name);

        if ($eppDomainInfo['status'] !== Config::get('domain.status.ok')) {
            return [];
        }

        $now = Carbon::now();
        $contacts = $this->getDefaultContacts($localInfo->user_id)[$localInfo->registry_id];
        $registrantName = $contacts['name'][DomainContact::REGISTRANT];
        $registrantId = $contacts['id'][DomainContact::REGISTRANT];
        unset($contacts['name'][DomainContact::REGISTRANT]);
        unset($contacts['id'][DomainContact::REGISTRANT]);
        $lockinPeriod = intval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_LOCKIN_PERIOD));
        $status = EppDomainStatus::CLIENT_LOCK_STATUS;
        $newNameservers = $eppDomainInfo['data']['nameservers'];

        $domainEppContact = [
            'name' => $name,
            'registrant' => $registrantName,
            'contacts' => (object) $contacts['name'],
            'regenerateAuthCode' => true,
        ];

        $domainEppStatus = [
            'name' => $name,
            'status' => $status,
        ];

        $domainLocal = [
            'id' => $localInfo->domain_id,
            'name' => $name,
            'transferredIn' => Carbon::parse($transferredIn),
            'status' => DomainStatus::ACTIVE,
            'client_status' => json_encode($status),
            'registrant' => $registrantName,
            'nameservers' => json_encode($newNameservers),
            'contacts' => json_encode($contacts['name']),
            'created_at' => Carbon::parse($eppDomainInfo['data']['created']),
            'updated_at' => Carbon::parse($eppDomainInfo['data']['updated']),
            'server_renew_at' => $now,
            'client_renew_at' => $now,
            'auth_code_updated_at' => $now,
            'expiry' => Carbon::parse($eppDomainInfo['data']['expiry'])->valueOf(),
        ];

        $registeredDomain = [
            'id' => $localInfo->registered_domain_id,
            'name' => $name,
            'status' => UserDomainStatus::OWNED,
            'user_contact_registrar_id' => $registrantId,
            'contacts_id' => json_encode($contacts['id']),
            'user_category_id' => DomainRegistrationService::instance()->getCategory($localInfo->user_id),
            'locked_until' => $now->addDays($lockinPeriod)->timestamp,
            'updated_at' => $now,
        ];

        return [
            'domainEppContact' => $domainEppContact,
            'domainEppStatus' => $domainEppStatus,
            'domainLocal' => $domainLocal,
            'registeredDomain' => $registeredDomain,
        ];
    }

    private function updateDomainDetails($updatePayload): void
    {
        $domainLocal = $updatePayload['domainLocal'];
        $domainEppContact = $updatePayload['domainEppContact'];
        $domainEppStatus = $updatePayload['domainEppStatus'];
        $registeredDomain = $updatePayload['registeredDomain'];

        app(AuthLogger::class)->info($this->fromWho('Domain details update start...', 'Cron:'));
        $contactUpdateResponse = EppDomainService::instance()->updateEppDomain($domainEppContact, 'Cron:');

        if (array_key_exists('errors', $contactUpdateResponse)) {
            app(AuthLogger::class)->error($this->fromWho('Domain contact update has failed.', 'Cron:'));

            return;
        }

        $statusUpdateResponse = EppDomainService::instance()->updateEppDomain($domainEppStatus, 'Cron:');

        if (array_key_exists('errors', $statusUpdateResponse)) {
            app(AuthLogger::class)->error($this->fromWho('Domain status update has failed.', 'Cron:'));

            return;
        }

        $this->updateRegisteredDomain($registeredDomain);
        $this->updateDomain($domainLocal);
        app(AuthLogger::class)->info($this->fromWho('Updated Epp Domain Contacts of '.$domainLocal['name'], 'Cron:'));
        app(AuthLogger::class)->info($this->fromWho('Domain update end...', 'Cron:'));
    }

    private function updateDomain($domain): void
    {
        $id = $domain['id'];
        unset($domain['id']);
        $name = $domain['name'];
        unset($domain['name']);

        DB::table('domains')->where('id', $id)->update($domain);

        app(AuthLogger::class)->info($this->fromWho('Updated domain '.$name, 'Cron:'));
    }

    private function updateRegisteredDomain($registeredDomain): void
    {
        $id = $registeredDomain['id'];
        unset($registeredDomain['id']);
        $name = $registeredDomain['name'];
        unset($registeredDomain['name']);

        DB::table('registered_domains')->where('id', $id)->update($registeredDomain);

        app(AuthLogger::class)->info($this->fromWho('Updated Registered Domain id: '.$id, 'Cron:'));
    }
}
